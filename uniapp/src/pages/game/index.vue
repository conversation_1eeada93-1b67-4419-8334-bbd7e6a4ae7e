<template>
  <view class="game-page-container">


    <view v-if="selectedLibraryInfo" class="selected-library-info card">
      <text class="library-name">{{ selectedLibraryInfo.name }}</text>
      <text class="library-description">{{ selectedLibraryInfo.description }}</text>
    </view>

    <!-- 游戏区域 -->
    <view v-if="currentLevel" class="game-area card">
      <view class="game-info-bar">
        <text>关卡: {{ currentLevel.name }}</text>
        <text>分数: {{ gameScore }}</text>
        <text>已配对: {{ matchedPairs }}/{{ totalPairs }}</text>
      </view>

      <view class="game-board">
        <view
          v-for="(tile, index) in gameBoard"
          :key="tile.id"
          class="board-tile"
          :class="{
            'selected': tile.selected,
            'matched': tile.matched
          }"
          :style="{ backgroundColor: tile.color }"
          @click="handleTileClick(index)"
        >
          <text class="tile-word">{{ tile.word ? tile.word.english : '' }}</text>
          <text class="tile-chinese">{{ tile.word ? tile.word.chinese : '' }}</text>
        </view>
      </view>

      <view class="game-controls">
        <button @click="resetGame" class="control-button">重置</button>
        <button @click="goBackHome" class="control-button">返回</button>
      </view>
    </view>

    <!-- 关卡完成弹窗 -->
    <view v-if="isGameEndModalVisible" class="modal-overlay">
      <view class="modal-content">
        <text class="modal-title">{{ gameResultText }}</text>
        <text>您的得分: {{ gameScore }}</text>
        <view class="modal-buttons">
          <button @click="nextLevel" class="modal-button primary" v-if="gameWon && canGoToNextLevel">下一关</button>
          <button @click="resetGame" class="modal-button">再试一次</button>
          <button @click="goBackHome" class="modal-button">返回首页</button>
        </view>
      </view>
    </view>


  </view>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { onLoad } from '@dcloudio/uni-app';

const selectedLibraryInfo = ref(null);
const currentLevel = ref(null); // 当前关卡
const currentLevelId = ref(1); // 当前关卡ID

// 删除了关卡选择相关变量

const gameBoard = ref([]);
const gameScore = ref(0);
const selectedTiles = ref([]); // 存储用户选择的卡片
const matchedPairs = ref(0); // 已配对数量
const totalPairs = ref(0); // 总配对数量
const isChecking = ref(false); // 是否正在检查匹配

const BOARD_SIZE = 16; // 4x4 网格，总共16张卡片（8对）
const CARD_COLORS = ['#FFB6C1', '#ADD8E6', '#90EE90', '#FFD700', '#FFA07A', '#DDA0DD', '#F0E68C', '#FFA500']; // 8种颜色
let tileIdCounter = 0;

const isGameEndModalVisible = ref(false);
const gameResultText = ref('');
const gameWon = ref(false);

// 计算属性：是否可以进入下一关
const canGoToNextLevel = computed(() => {
  if (!selectedLibraryInfo.value || !selectedLibraryInfo.value.words) return false;
  const maxLevels = Math.min(Math.floor(selectedLibraryInfo.value.words.length / 8), 1000);
  return currentLevelId.value < maxLevels;
});

// 获取当前关卡的单词
const wordsForCurrentLevel = computed(() => {
  if (selectedLibraryInfo.value && selectedLibraryInfo.value.words) {
    // 每个关卡使用8个不同的单词，循环使用词库中的单词
    const words = selectedLibraryInfo.value.words;
    const startIndex = ((currentLevelId.value - 1) * 8) % words.length;
    const selectedWords = [];

    for (let i = 0; i < 8; i++) {
      const wordIndex = (startIndex + i) % words.length;
      selectedWords.push(words[wordIndex]);
    }

    return selectedWords;
  }
  return [];
});

onLoad(() => {
  // 页面加载时，从本地存储获取选择的词库信息
  const libraryData = uni.getStorageSync('selectedLibrary');
  if (libraryData) {
    try {
      selectedLibraryInfo.value = JSON.parse(libraryData);

      // 获取当前关卡进度
      const savedLevelId = getCurrentLevelId(selectedLibraryInfo.value.id);
      currentLevelId.value = savedLevelId;

      // 设置当前关卡信息
      currentLevel.value = {
        id: currentLevelId.value,
        name: `第${currentLevelId.value}关`,
        wordsCount: 8
      };

      // 初始化游戏
      resetGame();

    } catch (e) {
      console.error("Failed to parse selected library data:", e);
      uni.showToast({ title: '加载词库数据失败', icon: 'none' });
      goBackHome();
    }
  } else {
    uni.showToast({ title: '未选择词库', icon: 'none' });
    goBackHome(); // 如果没有词库信息，则返回首页
  }
});

// 获取当前关卡进度
const getCurrentLevelId = (libraryId) => {
  const key = `currentLevel_${libraryId}`;
  const saved = uni.getStorageSync(key);
  return saved ? parseInt(saved) : 1; // 默认第一关
};

// 保存当前关卡进度
const saveCurrentLevelId = (libraryId, levelId) => {
  const key = `currentLevel_${libraryId}`;
  uni.setStorageSync(key, levelId.toString());
};

// 删除了关卡初始化相关函数

const initializeGameBoard = () => {
  const words = wordsForCurrentLevel.value;
  if (words.length < 8) {
    uni.showToast({ title: '词汇数量不足', icon: 'none' });
    return;
  }

  // 创建卡片对：每个单词创建两张卡片
  const cards = [];
  for (let i = 0; i < 8; i++) {
    const word = words[i];
    const color = CARD_COLORS[i];

    // 创建两张相同的卡片
    for (let j = 0; j < 2; j++) {
      cards.push({
        id: tileIdCounter++,
        word: word,
        color: color,
        selected: false,
        matched: false,
        pairId: i // 用于标识配对
      });
    }
  }

  // 随机打乱卡片顺序
  for (let i = cards.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [cards[i], cards[j]] = [cards[j], cards[i]];
  }

  gameBoard.value = cards;
  totalPairs.value = 8;
  matchedPairs.value = 0;
};

const handleTileClick = (index) => {
  // 如果正在检查匹配或游戏结束，不允许点击
  if (isChecking.value || isGameEndModalVisible.value) return;

  const tile = gameBoard.value[index];

  // 如果卡片已经匹配，不允许点击
  if (tile.matched) return;

  // 如果已经选择了两张卡片，不允许再选择
  if (selectedTiles.value.length >= 2) return;

  // 如果点击的是已经选中的卡片，取消选择
  if (tile.selected) {
    tile.selected = false;
    selectedTiles.value = selectedTiles.value.filter(item => item.index !== index);
    return;
  }

  // 选择卡片
  tile.selected = true;
  selectedTiles.value.push({ index, tile });

  // 如果选择了两张卡片，检查是否匹配
  if (selectedTiles.value.length === 2) {
    isChecking.value = true;
    setTimeout(() => {
      checkMatch();
    }, 500); // 延迟0.5秒让用户看清楚选择
  }
};

const checkMatch = () => {
  const [tile1, tile2] = selectedTiles.value;

  // 检查两张卡片的单词是否相同（通过pairId判断）
  if (tile1.tile.pairId === tile2.tile.pairId) {
    // 匹配成功
    tile1.tile.matched = true;
    tile2.tile.matched = true;
    tile1.tile.selected = false;
    tile2.tile.selected = false;

    matchedPairs.value++;
    gameScore.value += 20; // 每对匹配20分

    uni.showToast({
      title: '配对成功！+20分',
      icon: 'success',
      duration: 1000
    });

    // 检查是否完成游戏
    if (matchedPairs.value === totalPairs.value) {
      setTimeout(() => {
        endGame(true);
      }, 1000);
    }
  } else {
    // 匹配失败，取消选择
    tile1.tile.selected = false;
    tile2.tile.selected = false;

    uni.showToast({
      title: '配对失败，请重新选择',
      icon: 'none',
      duration: 800
    });
  }

  // 清除选择状态
  selectedTiles.value = [];
  isChecking.value = false;
};

// 返回首页
const goBackHome = () => {
  uni.navigateBack({ delta: 1 });
};

// 重置游戏
const resetGame = () => {
  gameScore.value = 0;
  selectedTiles.value = [];
  matchedPairs.value = 0;
  isChecking.value = false;
  isGameEndModalVisible.value = false;
  initializeGameBoard();
};

// 下一关
const nextLevel = () => {
  if (canGoToNextLevel.value) {
    currentLevelId.value++;
    currentLevel.value = {
      id: currentLevelId.value,
      name: `第${currentLevelId.value}关`,
      wordsCount: 8
    };

    // 保存进度
    if (selectedLibraryInfo.value) {
      saveCurrentLevelId(selectedLibraryInfo.value.id, currentLevelId.value);
    }

    // 重置游戏
    resetGame();

    uni.showToast({
      title: `进入第${currentLevelId.value}关`,
      icon: 'success',
      duration: 1000
    });
  } else {
    uni.showToast({
      title: '已经是最后一关了',
      icon: 'none',
      duration: 1000
    });
  }
};

const endGame = (won) => {
  gameWon.value = won;
  gameResultText.value = won ? '恭喜过关！' : '挑战失败！';
  isGameEndModalVisible.value = true;
  if (won) {
    // 更新总分
    const currentTotalScore = parseInt(uni.getStorageSync('englishGameTotalScore') || '0');
    uni.setStorageSync('englishGameTotalScore', currentTotalScore + gameScore.value);
  }
};

// 删除了抽屉相关操作函数
</script>

<style scoped>
.game-page-container {
  min-height: 100vh;
  background-color: #f0e6ff; /* 淡紫色背景 */
  padding: 16rpx;
}


.card { /* Common card style */
  background-color: white;
  border-radius: 24rpx; /* rounded-xl or 2xl */
  padding: 16rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 8rpx rgba(0,0,0,0.1);
}



.library-name {
  display: block;
  font-size: 36rpx; /* text-xl */
  font-weight: 600; /* font-semibold */
  color: #6d28d9; /* text-purple-700 */
  margin-bottom: 8rpx;
}

.library-description {
  display: block;
  font-size: 24rpx; /* text-sm */
  color: #7e22ce; /* text-purple-600 */
  line-height: 1.5;
}

/* 删除了关卡选择相关的样式 */
.game-area {
  /* padding already from .card */
  margin-top: 32rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.game-info-bar {
  display: flex;
  justify-content: space-around;
  width: 100%;
  padding: 10rpx 0;
  margin-bottom: 20rpx;
  font-size: 28rpx;
  color: #333;
}
.game-board {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-template-rows: repeat(4, 1fr);
  width: 600rpx;
  height: 600rpx;
  gap: 10rpx;
  margin: 0 auto 20rpx;
  padding: 20rpx;
  background-color: #f5f5f5;
  border-radius: 16rpx;
}

.board-tile {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
  cursor: pointer;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
  border: 3rpx solid transparent;
}

.board-tile:hover {
  transform: scale(1.05);
  box-shadow: 0 6rpx 12rpx rgba(0, 0, 0, 0.15);
}

.board-tile.selected {
  transform: scale(1.1);
  border-color: #ff4500;
  box-shadow: 0 8rpx 16rpx rgba(255, 69, 0, 0.3);
}

.board-tile.matched {
  opacity: 0.6;
  transform: scale(0.95);
  border-color: #4caf50;
  box-shadow: 0 4rpx 8rpx rgba(76, 175, 80, 0.3);
}

.tile-word {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
  text-align: center;
}

.tile-chinese {
  font-size: 24rpx;
  color: #666;
  text-align: center;
}
.game-controls {
  display: flex;
  justify-content: center;
  gap: 24rpx;
  width: 100%;
  margin-top: 20rpx;
}
.control-button {
  padding: 12rpx 24rpx;
  font-size: 24rpx;
  border-radius: 20rpx;
  border: none;
  background-color: #007aff;
  color: white;
  transition: background-color 0.3s ease;
  min-width: 120rpx;
}

.control-button:disabled {
  background-color: #ccc;
  color: #999;
}

.control-button:hover:not(:disabled) {
  background-color: #0056b3;
}
.game-start-text {
  display: block;
  font-size: 36rpx; /* text-xl */
  font-weight: bold;
}
/* Modal Styles (simplified) */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}
.modal-content {
  background-color: white;
  padding: 40rpx;
  border-radius: 16rpx;
  text-align: center;
  min-width: 500rpx;
}
.modal-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  display: block;
}
.modal-buttons {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  margin-top: 32rpx;
}

.modal-button {
  padding: 16rpx 32rpx;
  font-size: 28rpx;
  border-radius: 24rpx;
  border: none;
  background-color: #f0f0f0;
  color: #333;
  transition: background-color 0.3s ease;
}

.modal-button.primary {
  background-color: #007aff;
  color: white;
}

.modal-button:hover {
  opacity: 0.8;
}

/* 删除了抽屉相关样式 */
</style>