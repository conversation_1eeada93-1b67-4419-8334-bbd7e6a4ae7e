<template>
  <view class="home-container">
    <!-- 标题 -->
    <view class="section-title">
      <text class="title-text">选择关卡</text>
      <text class="title-subtitle">选择你想要挑战的关卡开始游戏吧！</text>
    </view>

    <!-- 关卡列表 -->
    <view class="levels-grid">
      <view
        v-for="level in levels"
        :key="level.id"
        class="level-card"
        :class="{
          'level-locked': level.locked,
          'level-completed': level.completed
        }"
        hover-class="level-card-hover"
      >
        <view class="level-number" @click="selectLevel(level)">{{ level.levelNumber }}</view>
        <view class="level-info" @click="selectLevel(level)">
          <text class="level-name">{{ level.name }}</text>
          <text class="level-desc">{{ level.description }}</text>
        </view>
        <view class="level-actions">
          <!-- 未解锁状态 -->
          <view v-if="level.locked" class="level-status">
            <text class="lock-text">未解锁</text>
          </view>
          <!-- 已完成状态 -->
          <view v-else-if="level.completed" class="level-status">
            <text class="completed-text">已完成</text>
            <view class="replay-btn" @click.stop="replayLevel(level)">
              <text class="replay-text">重玩</text>
            </view>
          </view>
          <!-- 未完成状态 -->
          <view v-else class="level-status">
            <text class="start-text">开始</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部装饰 -->
    <view class="bottom-decoration">
      <text class="decoration-text">挑战更多关卡，提升你的技能！</text>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';

// 词库数据
const libraries = [
  {
    id: 1,
    name: '小学基础词汇',
    words: [
      { english: 'apple', chinese: '苹果', phonetic: '/ˈæpl/' },
      { english: 'book', chinese: '书', phonetic: '/bʊk/' },
      { english: 'cat', chinese: '猫', phonetic: '/kæt/' },
      { english: 'dog', chinese: '狗', phonetic: '/dɔːɡ/' },
      { english: 'egg', chinese: '鸡蛋', phonetic: '/eɡ/' },
      { english: 'fish', chinese: '鱼', phonetic: '/fɪʃ/' },
      { english: 'green', chinese: '绿色', phonetic: '/ɡriːn/' },
      { english: 'house', chinese: '房子', phonetic: '/haʊs/' },
      { english: 'ice', chinese: '冰', phonetic: '/aɪs/' },
      { english: 'jump', chinese: '跳', phonetic: '/dʒʌmp/' }
    ]
  },
  {
    id: 2,
    name: '动物世界',
    words: [
      { english: 'lion', chinese: '狮子', phonetic: '/ˈlaɪən/' },
      { english: 'tiger', chinese: '老虎', phonetic: '/ˈtaɪɡər/' },
      { english: 'elephant', chinese: '大象', phonetic: '/ˈelɪfənt/' },
      { english: 'monkey', chinese: '猴子', phonetic: '/ˈmʌŋki/' },
      { english: 'rabbit', chinese: '兔子', phonetic: '/ˈræbɪt/' },
      { english: 'bird', chinese: '鸟', phonetic: '/bɜːrd/' },
      { english: 'horse', chinese: '马', phonetic: '/hɔːrs/' },
      { english: 'cow', chinese: '牛', phonetic: '/kaʊ/' }
    ]
  },
  {
    id: 3,
    name: '颜色彩虹',
    words: [
      { english: 'red', chinese: '红色', phonetic: '/red/' },
      { english: 'blue', chinese: '蓝色', phonetic: '/bluː/' },
      { english: 'yellow', chinese: '黄色', phonetic: '/ˈjeloʊ/' },
      { english: 'green', chinese: '绿色', phonetic: '/ɡriːn/' },
      { english: 'purple', chinese: '紫色', phonetic: '/ˈpɜːrpl/' },
      { english: 'orange', chinese: '橙色', phonetic: '/ˈɔːrɪndʒ/' },
      { english: 'pink', chinese: '粉色', phonetic: '/pɪŋk/' },
      { english: 'black', chinese: '黑色', phonetic: '/blæk/' },
      { english: 'white', chinese: '白色', phonetic: '/waɪt/' }
    ]
  },
  {
    id: 4,
    name: '数字王国',
    words: [
      { english: 'one', chinese: '一', phonetic: '/wʌn/' },
      { english: 'two', chinese: '二', phonetic: '/tuː/' },
      { english: 'three', chinese: '三', phonetic: '/θriː/' },
      { english: 'four', chinese: '四', phonetic: '/fɔːr/' },
      { english: 'five', chinese: '五', phonetic: '/faɪv/' },
      { english: 'six', chinese: '六', phonetic: '/sɪks/' },
      { english: 'seven', chinese: '七', phonetic: '/ˈsevn/' },
      { english: 'eight', chinese: '八', phonetic: '/eɪt/' },
      { english: 'nine', chinese: '九', phonetic: '/naɪn/' },
      { english: 'ten', chinese: '十', phonetic: '/ten/' }
    ]
  }
];

// 关卡数据
const levels = ref([
  {
    id: 1,
    levelNumber: '01',
    name: '基础入门',
    description: '小学基础词汇',
    library: libraries[0],
    locked: false,
    completed: false
  },
  {
    id: 2,
    levelNumber: '02',
    name: '动物乐园',
    description: '认识可爱动物',
    library: libraries[1],
    locked: false,
    completed: false
  },
  {
    id: 3,
    levelNumber: '03',
    name: '彩色世界',
    description: '学习颜色词汇',
    library: libraries[2],
    locked: false,
    completed: false
  },
  {
    id: 4,
    levelNumber: '04',
    name: '数字天地',
    description: '掌握数字表达',
    library: libraries[3],
    locked: false,
    completed: false
  },
  {
    id: 5,
    levelNumber: '05',
    name: '进阶挑战',
    description: '混合词汇练习',
    library: libraries[0], // 可以重复使用词库
    locked: true,
    completed: false
  },
  {
    id: 6,
    levelNumber: '06',
    name: '高级训练',
    description: '综合能力测试',
    library: libraries[1],
    locked: true,
    completed: false
  }
]);

// 页面加载时初始化关卡状态
onMounted(() => {
  loadLevelProgress();
});

// 加载关卡进度
const loadLevelProgress = () => {
  levels.value.forEach(level => {
    const completedKey = `level_${level.id}_completed`;
    level.completed = uni.getStorageSync(completedKey) === 'true';
  });
};

// 保存关卡完成状态
const saveLevelProgress = (levelId, completed) => {
  const completedKey = `level_${levelId}_completed`;
  uni.setStorageSync(completedKey, completed.toString());
};

// 选择关卡
const selectLevel = (level) => {
  if (level.locked) {
    uni.showToast({
      title: '该关卡尚未解锁',
      icon: 'none',
      duration: 1500
    });
    return;
  }

  console.log('Selected level:', level);

  // 将选择的关卡信息存储到本地
  uni.setStorageSync('selectedLibrary', JSON.stringify(level.library));
  uni.setStorageSync('selectedLevel', JSON.stringify(level));

  // 显示选择提示
  const actionText = level.completed ? '重新挑战' : '进入关卡';
  uni.showToast({
    title: `${actionText}: ${level.name}`,
    icon: 'success',
    duration: 1500
  });

  // 延迟跳转，让用户看到提示
  setTimeout(() => {
    uni.navigateTo({
      url: '/pages/game/index'
    });
  }, 1500);
};

// 重玩关卡
const replayLevel = (level) => {
  console.log('Replay level:', level);

  // 将选择的关卡信息存储到本地
  uni.setStorageSync('selectedLibrary', JSON.stringify(level.library));
  uni.setStorageSync('selectedLevel', JSON.stringify(level));

  // 显示重玩提示
  uni.showToast({
    title: `重玩关卡: ${level.name}`,
    icon: 'success',
    duration: 1500
  });

  // 延迟跳转，让用户看到提示
  setTimeout(() => {
    uni.navigateTo({
      url: '/pages/game/index'
    });
  }, 1500);
};


</script>

<style scoped>
.home-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 50%, #fd79a8 100%);
  padding: 32rpx 24rpx;
}

/* 标题区域 */
.section-title {
  text-align: center;
  margin-bottom: 48rpx;
  margin-top: 40rpx;
}

.title-text {
  display: block;
  font-size: 52rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 16rpx;
  text-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.2);
  letter-spacing: 2rpx;
}

.title-subtitle {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.95);
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

/* 关卡网格 */
.levels-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 24rpx;
  margin-bottom: 40rpx;
  padding: 0 8rpx;
}

.level-card {
  background: white;
  border-radius: 32rpx;
  padding: 32rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.level-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6rpx;
  background: linear-gradient(90deg, #74b9ff, #0984e3);
}

.level-card-hover {
  transform: translateY(-8rpx) scale(1.02);
  box-shadow: 0 16rpx 40rpx rgba(0, 0, 0, 0.12);
}

.level-card.level-locked {
  opacity: 0.6;
  background: #f8f9fa;
}

.level-card.level-locked::before {
  background: #ddd;
}

.level-card.level-completed::before {
  background: linear-gradient(90deg, #00b894, #00a085);
}

.level-number {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #74b9ff, #0984e3);
  color: white;
  font-size: 32rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(116, 185, 255, 0.3);
  cursor: pointer;
}

.level-locked .level-number {
  background: #bbb;
  box-shadow: 0 4rpx 12rpx rgba(187, 187, 187, 0.3);
}

.level-completed .level-number {
  background: linear-gradient(135deg, #00b894, #00a085);
  box-shadow: 0 4rpx 12rpx rgba(0, 184, 148, 0.3);
}

.level-info {
  flex: 1;
  cursor: pointer;
}

.level-name {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #2d3748;
  margin-bottom: 8rpx;
  line-height: 1.2;
}

.level-desc {
  display: block;
  font-size: 24rpx;
  color: #718096;
  line-height: 1.3;
}

.level-locked .level-name,
.level-locked .level-desc {
  color: #999;
}

.level-actions {
  margin-left: 16rpx;
  display: flex;
  align-items: center;
}

.level-status {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.lock-text {
  font-size: 22rpx;
  color: #999;
  background: #f0f0f0;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}

.completed-text {
  font-size: 22rpx;
  color: #00b894;
  background: #d1f2eb;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-weight: 500;
}

.start-text {
  font-size: 22rpx;
  color: #74b9ff;
  background: #e3f2fd;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-weight: 500;
}

.replay-btn {
  background: linear-gradient(135deg, #fd79a8, #e84393);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(232, 67, 147, 0.3);
}

.replay-btn:hover {
  transform: scale(1.05);
  box-shadow: 0 4rpx 12rpx rgba(232, 67, 147, 0.4);
}

.replay-text {
  font-size: 22rpx;
  color: white;
  font-weight: 500;
}



/* 底部装饰 */
.bottom-decoration {
  text-align: center;
  padding: 48rpx 0 32rpx;
}

.decoration-text {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
  font-weight: 500;
}
</style>