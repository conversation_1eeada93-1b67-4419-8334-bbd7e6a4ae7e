# 🎮 XX 消消乐 - English Match Game

一个基于 uniapp + Vue3 + TypeScript 开发的 XX 学习消消乐游戏，采用可爱风格设计，让孩子在游戏中快乐学习 XX 单词。

## ✨ 功能特色

### 🏠 首页功能

- **用户信息展示**：显示学习等级、总积分和学习进度
- **词库选择**：提供多个主题词库（基础词汇、动物世界、颜色彩虹、数字王国）
- **词汇预览**：每个词库都有词汇预览功能
- **学习进度**：可视化显示每个词库的学习进度
- **可爱界面**：渐变背景和卡片式设计，符合儿童审美

### 🎮 游戏功能

- **简洁游戏**：选择词库后直接进入游戏，界面简洁清爽
- **单词配对游戏**：点击两个相同单词进行配对
- **单词学习**：每张卡片显示完整的 XX 单词和中文翻译
- **关卡进度**：自动记录当前关卡进度，支持最多 1000 关
- **关卡切换**：完成关卡后可直接进入下一关
- **积分系统**：成功配对获得积分，激励持续学习
- **进度保存**：使用 localStorage 保存游戏进度和积分
- **小巧控制**：精简的操作按钮，只保留核心功能

## 🛠️ 技术栈

- **框架**：uniapp + Vue3
- **语言**：TypeScript
- **样式**：SCSS + uni-ui
- **状态管理**：Vue3 Composition API
- **数据存储**：localStorage

## 📱 支持平台

- H5 (Web)
- 微信小程序
- 支付宝小程序
- App (Android/iOS)
- 其他小程序平台

## 🚀 快速开始

### 安装依赖

```bash
npm install
# 或
yarn install
```

### 开发运行

```bash
# H5开发
npm run dev:h5

# 微信小程序开发
npm run dev:mp-weixin

# App开发
npm run dev:app
```

### 构建发布

```bash
# H5构建
npm run build:h5

# 微信小程序构建
npm run build:mp-weixin

# App构建
npm run build:app
```

## 📁 项目结构

```
src/
├── pages/
│   ├── index/           # 首页
│   │   └── index.vue
│   └── game/            # 游戏页面
│       └── index.vue
├── static/              # 静态资源
├── App.vue             # 应用入口
├── main.ts             # 主入口文件
├── pages.json          # 页面配置
├── manifest.json       # 应用配置
└── uni.scss           # 全局样式变量
```

## 🎯 游戏玩法

1. **选择词库**：在首页选择想要学习的词库主题
2. **直接开始**：选择词库后直接进入游戏界面，简洁清爽
3. **单词配对游戏**：
   - 游戏开始时，所有卡片都显示 XX 单词和中文翻译
   - 点击两张相同单词的卡片进行配对
   - 配对成功获得 20 分，卡片变为已匹配状态
   - 配对失败则取消选择，重新选择
   - 找到所有 8 对相同单词即可过关
4. **游戏控制**：
   - **重置**：重新开始当前关卡
   - **返回**：返回首页选择其他词库
5. **关卡进度**：
   - 完成关卡后可选择进入下一关或返回首页
   - 系统自动记录每个词库的当前关卡进度
   - 下次进入该词库时从上次的进度继续

## 🎨 设计特色

- **可爱风格**：采用温馨的色彩搭配和圆润的设计元素
- **渐变背景**：美观的渐变色背景，提升视觉体验
- **卡片设计**：信息以卡片形式展示，层次分明
- **动画效果**：流畅的过渡动画和交互反馈
- **响应式布局**：适配不同屏幕尺寸

## 📚 词库内容

### 🎒 小学基础词汇

包含日常生活中常用的基础 XX 单词，适合初学者。

### 🦁 动物世界

各种可爱动物的 XX 单词，让孩子认识大自然。

### 🌈 颜色彩虹

学习各种颜色的 XX 表达，让世界更加绚烂。

### 🔢 数字王国

基础数字 XX，为数学学习打下基础。

## 🔧 自定义扩展

### 添加新词库

在 `pages/index/index.vue` 的 `libraries` 数组中添加新的词库对象：

```javascript
{
  id: 5,
  name: '新词库名称',
  icon: '🎯',
  difficulty: '中级',
  description: '词库描述',
  progress: 0,
  words: [
    { english: 'word', chinese: '单词', phonetic: '/wɜːrd/' },
    // 更多单词...
  ]
}
```

### 调整游戏难度

在 `pages/game/index.vue` 中可以调整：

- `BOARD_SIZE`：卡片数量（当前为 16 张卡片，8 对）
- 每关的单词数量
- 配对成功的得分

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目！

---

**让学习变得更有趣！🌟**
